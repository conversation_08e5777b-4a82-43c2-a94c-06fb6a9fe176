function AdjObjThreat  = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock)

    current_t = single(clock);
    % calculate long and lat threat coeff
    % weight long and lat threat coeff
    % get final threat coeff
    returnCond = (obj_id == uint64(0)) || (x_lead > 30) || (abs(y_lead) > 4);
    if returnCond
        AdjObjThreat = single(0);
        return;
    end

    % calcualte long threat coeff
    tau = single(2);

    v_lead = max(0.1, vx_lead);
    x_lead = max(0.5, x_lead);
    min_rel_spd = single(-1);

    rel_spd = v_lead - v_host;
    if rel_spd < min_rel_spd
        TTC = single(x_lead / abs(rel_spd));
        TTC = min(TTC, 20);
    else
        TTC = single(21);
    end
    
    if rel_spd > 3
        long_threat = single(0);
    else
        long_threat = single(exp(-TTC / tau));
        long_threat = max(min(long_threat, 1), 0);
    end
    

    % calculate lat threat coeff
    t_pred = single(0.5);
    w_lane = single(3.5);
    sigma_y = single(w_lane / 3);

    vy_lead_correct = vy_lead - yawrate * x_lead;
    y_lead = y_lead + t_pred * vy_lead_correct;
    if abs(y_lead) < 2 % 2 half veh width
        dist_side2side = single(0);
    else
        dist_side2side = single(abs(y_lead) - 2); % 2m means half vehicle width combined (ego veh and adj veh)
    end
   
    lat_threat = single(exp(-0.5 * (dist_side2side / sigma_y)^2));
    lat_threat = max(min(lat_threat, 1), 0);

    % 动态权重调整策略
    [weight_long, weight_lat] = calculateDynamicWeights(x_lead, rel_spd, vy_lead_correct, y_lead, v_host);

    % get final threat coeff
    AdjObjThreat = single(weight_long * long_threat + weight_lat * lat_threat);
    fprintf('threat_long = %f; threat_lat = %f; dist_side2side = %f; weight_long = %f; weight_lat = %f; AdjObjThreat = %f; current time = %f\n', long_threat, lat_threat, dist_side2side, weight_long, weight_lat, AdjObjThreat, current_t);

end

function [weight_long, weight_lat] = calculateDynamicWeights(x_lead, rel_spd, vy_lead_correct, y_lead, v_host)
    % 动态权重计算函数
    % 根据场景特征动态调整纵向和横向威胁的权重

    % 基础权重（默认情况）
    base_weight_long = single(0.3);
    base_weight_lat = single(0.7);

    % 场景分析参数
    close_distance_threshold = single(15);      % 近距离阈值 (m)
    high_speed_threshold = single(20);          % 高速阈值 (m/s)
    lane_change_speed_threshold = single(0.3);  % 变道速度阈值 (m/s)
    aggressive_rel_spd_threshold = single(-3);  % 激进相对速度阈值 (m/s)

    % 初始化权重调整因子
    long_weight_factor = single(1.0);
    lat_weight_factor = single(1.0);

    %% 场景1: 近距离场景 - 增加纵向权重
    if x_lead < close_distance_threshold
        distance_factor = (close_distance_threshold - x_lead) / close_distance_threshold;
        long_weight_factor = long_weight_factor + 0.4 * distance_factor;
        fprintf('  [权重调整] 近距离场景: 距离=%.1fm, 纵向权重增加%.2f\n', x_lead, 0.4 * distance_factor);
    end

    %% 场景2: 高速场景 - 增加纵向权重
    if v_host > high_speed_threshold
        speed_factor = min((v_host - high_speed_threshold) / 10, 1.0);
        long_weight_factor = long_weight_factor + 0.3 * speed_factor;
        fprintf('  [权重调整] 高速场景: 自车速度=%.1fm/s, 纵向权重增加%.2f\n', v_host, 0.3 * speed_factor);
    end

    %% 场景3: 激进相对速度 - 大幅增加纵向权重
    if rel_spd < aggressive_rel_spd_threshold
        aggressive_factor = min(abs(rel_spd - aggressive_rel_spd_threshold) / 5, 1.0);
        long_weight_factor = long_weight_factor + 0.5 * aggressive_factor;
        fprintf('  [权重调整] 激进相对速度: rel_spd=%.1fm/s, 纵向权重增加%.2f\n', rel_spd, 0.5 * aggressive_factor);
    end

    %% 场景4: 检测到变道意图 - 增加横向权重
    if abs(vy_lead_correct) > lane_change_speed_threshold
        % 判断变道方向和威胁程度
        if (y_lead > 0 && vy_lead_correct < -lane_change_speed_threshold) || ...
           (y_lead < 0 && vy_lead_correct > lane_change_speed_threshold)
            % 朝向自车道变道 - 高威胁
            lane_change_factor = min(abs(vy_lead_correct) / 2, 1.0);
            lat_weight_factor = lat_weight_factor + 0.6 * lane_change_factor;
            fprintf('  [权重调整] 朝向自车道变道: vy=%.2fm/s, 横向权重增加%.2f\n', vy_lead_correct, 0.6 * lane_change_factor);
        else
            % 远离自车道变道 - 中等威胁
            lane_change_factor = min(abs(vy_lead_correct) / 2, 1.0);
            lat_weight_factor = lat_weight_factor + 0.2 * lane_change_factor;
            fprintf('  [权重调整] 远离自车道变道: vy=%.2fm/s, 横向权重增加%.2f\n', vy_lead_correct, 0.2 * lane_change_factor);
        end
    end

    %% 场景5: 横向距离很近 - 增加横向权重
    if abs(y_lead) < 2.5  % 车辆横向距离很近
        proximity_factor = (2.5 - abs(y_lead)) / 2.5;
        lat_weight_factor = lat_weight_factor + 0.3 * proximity_factor;
        fprintf('  [权重调整] 横向距离很近: y_lead=%.1fm, 横向权重增加%.2f\n', y_lead, 0.3 * proximity_factor);
    end

    %% 场景6: 相对速度为正且较大 - 降低纵向权重
    if rel_spd > 5  % 前车明显更快
        speed_diff_factor = min((rel_spd - 5) / 10, 0.8);
        long_weight_factor = long_weight_factor - 0.4 * speed_diff_factor;
        fprintf('  [权重调整] 前车更快: rel_spd=%.1fm/s, 纵向权重减少%.2f\n', rel_spd, 0.4 * speed_diff_factor);
    end

    %% 计算最终权重
    adjusted_weight_long = base_weight_long * long_weight_factor;
    adjusted_weight_lat = base_weight_lat * lat_weight_factor;

    % 归一化权重，确保总和为1
    total_weight = adjusted_weight_long + adjusted_weight_lat;
    weight_long = single(adjusted_weight_long / total_weight);
    weight_lat = single(adjusted_weight_lat / total_weight);

    % 限制权重范围，避免极端情况
    weight_long = max(min(weight_long, 0.8), 0.1);  % 纵向权重范围 [0.1, 0.8]
    weight_lat = single(1.0 - weight_long);         % 横向权重 = 1 - 纵向权重

    fprintf('  [最终权重] 纵向: %.2f, 横向: %.2f\n', weight_long, weight_lat);
end