function AdjObjThreat  = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock)

    current_t = single(clock);
    % calculate long and lat threat coeff
    % weight long and lat threat coeff
    % get final threat coeff
    returnCond = (obj_id == uint64(0)) || (x_lead > 30) || (abs(y_lead) > 4);
    if returnCond
        AdjObjThreat = single(0);
        return;
    end

    % calcualte long threat coeff
    tau = single(2);

    v_lead = max(0.1, vx_lead);
    x_lead = max(0.5, x_lead);
    min_rel_spd = single(-1);

    rel_spd = v_lead - v_host;
    if rel_spd < min_rel_spd
        TTC = single(x_lead / rel_spd);
        TTC = min(TTC, 20);
    else
        TTC = single(21);
    end
    
    if rel_spd > 3
        long_threat = single(0);
    else
        long_threat = single(exp(-TTC / tau));
        long_threat = max(min(long_threat, 1), 0);
    end
    

    % calculate lat threat coeff
    t_pred = single(0.5);
    w_lane = single(3.5);
    sigma_y = single(w_lane / 3);

    vy_lead_correct = vy_lead - yawrate * x_lead;
    y_lead = y_lead + t_pred * vy_lead_correct;
    if abs(y_lead) < 2 % 2 half veh width
        dist_side2side = single(0);
    else
        dist_side2side = single(abs(y_lead) - 2); % 2m means half vehicle width combined (ego veh and adj veh)
    end
   
    lat_threat = single(exp(-0.5 * (dist_side2side / sigma_y)^2));
    lat_threat = max(min(lat_threat, 1), 0);

    % weight long and lat threat coeff
    weight_long = single(0.3);
    weight_lat = single(0.7);

    % get final threat coeff
    AdjObjThreat = single(weight_long * long_threat + weight_lat * lat_threat);
    fprintf('threat_long = %f; threat_lat = %f; dist_side2side = %f; AdjObjThreat = %f; current time = %f\n', long_threat, lat_threat, dist_side2side, AdjObjThreat, current_t);

end