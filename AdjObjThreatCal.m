function AdjObjThreat  = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock)

    current_t = single(clock);
    % calculate long and lat threat coeff
    % weight long and lat threat coeff
    % get final threat coeff
    returnCond = (obj_id == uint64(0)) || (x_lead > 30) || (abs(y_lead) > 4);
    if returnCond
        AdjObjThreat = single(0);
        return;
    end

    % calcualte long threat coeff
    tau = single(2);

    v_lead = max(0.1, vx_lead);
    x_lead = max(0.5, x_lead);
    min_rel_spd = single(-1);

    rel_spd = v_lead - v_host;
    if rel_spd < min_rel_spd
        TTC = single(x_lead / abs(rel_spd));
        TTC = min(TTC, 20);
    else
        TTC = single(21);
    end
    
    if rel_spd > 3
        long_threat = single(0);
    else
        long_threat = single(exp(-TTC / tau));
        long_threat = max(min(long_threat, 1), 0);
    end
    

    % calculate lat threat coeff
    t_pred = single(0.5);
    w_lane = single(3.5);
    sigma_y = single(w_lane / 3);

    vy_lead_correct = vy_lead - yawrate * x_lead;
    y_lead = y_lead + t_pred * vy_lead_correct;
    if abs(y_lead) < 2 % 2 half veh width
        dist_side2side = single(0);
    else
        dist_side2side = single(abs(y_lead) - 2); % 2m means half vehicle width combined (ego veh and adj veh)
    end
   
    lat_threat = single(exp(-0.5 * (dist_side2side / sigma_y)^2));
    lat_threat = max(min(lat_threat, 1), 0);

    % 动态权重调整策略
    [weight_long, weight_lat] = calculateDynamicWeights(x_lead, rel_spd, vy_lead_correct, y_lead, v_host);

    % get final threat coeff
    AdjObjThreat = single(weight_long * long_threat + weight_lat * lat_threat);
    fprintf('threat_long = %f; threat_lat = %f; dist_side2side = %f; weight_long = %f; weight_lat = %f; AdjObjThreat = %f; current time = %f\n', long_threat, lat_threat, dist_side2side, weight_long, weight_lat, AdjObjThreat, current_t);

end

function [weight_long, weight_lat] = calculateDynamicWeights(x_lead, rel_spd, vy_lead_correct, y_lead, v_host)
    % 相邻车道车辆动态权重计算函数
    % 针对相邻车道车辆的威胁特点进行权重调整

    % 基础权重（相邻车道默认横向威胁更重要）
    base_weight_long = single(0.2);  % 降低纵向基础权重
    base_weight_lat = single(0.8);   % 提高横向基础权重

    % 相邻车道场景分析参数
    overlap_zone_length = single(25);           % 重叠威胁区域长度 (m)
    lane_change_speed_threshold = single(0.2);  % 变道速度阈值 (m/s)
    high_lane_change_speed = single(0.8);       % 高变道速度阈值 (m/s)
    critical_lateral_distance = single(1.5);    % 临界横向距离 (m)
    speed_match_threshold = single(3);          % 速度匹配阈值 (m/s)

    % 初始化权重调整因子
    long_weight_factor = single(1.0);
    lat_weight_factor = single(1.0);

    %% 场景1: 纵向重叠区域 - 适度增加纵向权重
    if x_lead < overlap_zone_length && x_lead > -5  % 在重叠威胁区域内
        overlap_factor = (overlap_zone_length - x_lead) / overlap_zone_length;
        long_weight_factor = long_weight_factor + 0.3 * overlap_factor;
        fprintf('  [权重调整] 纵向重叠区域: 距离=%.1fm, 纵向权重增加%.2f\n', x_lead, 0.3 * overlap_factor);
    end

    %% 场景2: 速度匹配场景 - 增加纵向权重
    if abs(rel_spd) < speed_match_threshold  % 速度相近时
        speed_match_factor = (speed_match_threshold - abs(rel_spd)) / speed_match_threshold;
        long_weight_factor = long_weight_factor + 0.2 * speed_match_factor;
        fprintf('  [权重调整] 速度匹配: rel_spd=%.1fm/s, 纵向权重增加%.2f\n', rel_spd, 0.2 * speed_match_factor);
    end

    %% 场景3: 相邻车辆明显更快且在后方 - 增加纵向权重
    if rel_spd > 5 && x_lead < 0  % 后方快车
        overtake_factor = min((rel_spd - 5) / 10, 1.0);
        long_weight_factor = long_weight_factor + 0.4 * overtake_factor;
        fprintf('  [权重调整] 后方快车超越: rel_spd=%.1fm/s, 纵向权重增加%.2f\n', rel_spd, 0.4 * overtake_factor);
    end

    %% 场景4: 强变道意图 - 大幅增加横向权重
    if abs(vy_lead_correct) > lane_change_speed_threshold
        % 判断变道方向和威胁程度
        if (y_lead > 0 && vy_lead_correct < -lane_change_speed_threshold) || ...
           (y_lead < 0 && vy_lead_correct > lane_change_speed_threshold)
            % 朝向自车道变道 - 最高威胁
            lane_change_factor = min(abs(vy_lead_correct) / 1.5, 1.0);
            lat_weight_factor = lat_weight_factor + 1.0 * lane_change_factor;  % 大幅增加
            fprintf('  [权重调整] 朝向自车道变道: vy=%.2fm/s, 横向权重增加%.2f\n', vy_lead_correct, 1.0 * lane_change_factor);
        else
            % 远离自车道变道 - 威胁降低
            lane_change_factor = min(abs(vy_lead_correct) / 2, 1.0);
            lat_weight_factor = lat_weight_factor - 0.3 * lane_change_factor;  % 降低横向权重
            fprintf('  [权重调整] 远离自车道变道: vy=%.2fm/s, 横向权重减少%.2f\n', vy_lead_correct, 0.3 * lane_change_factor);
        end
    end

    %% 场景5: 激进变道 - 极高横向权重
    if abs(vy_lead_correct) > high_lane_change_speed
        aggressive_lane_change_factor = min(abs(vy_lead_correct) / 2, 1.0);
        lat_weight_factor = lat_weight_factor + 0.8 * aggressive_lane_change_factor;
        fprintf('  [权重调整] 激进变道: vy=%.2fm/s, 横向权重增加%.2f\n', vy_lead_correct, 0.8 * aggressive_lane_change_factor);
    end

    %% 场景6: 临界横向距离 - 增加横向权重
    if abs(y_lead) < critical_lateral_distance  % 已经很接近车道线
        critical_proximity_factor = (critical_lateral_distance - abs(y_lead)) / critical_lateral_distance;
        lat_weight_factor = lat_weight_factor + 0.5 * critical_proximity_factor;
        fprintf('  [权重调整] 临界横向距离: y_lead=%.1fm, 横向权重增加%.2f\n', y_lead, 0.5 * critical_proximity_factor);
    end

    %% 场景7: 相邻车道稳定行驶 - 降低整体威胁权重
    if abs(vy_lead_correct) < 0.1 && abs(y_lead) > 2.0  % 稳定在相邻车道
        stable_factor = min((abs(y_lead) - 2.0) / 2.0, 0.5);
        lat_weight_factor = lat_weight_factor - 0.2 * stable_factor;
        fprintf('  [权重调整] 稳定相邻车道行驶: 横向权重减少%.2f\n', 0.2 * stable_factor);
    end

    %% 计算最终权重
    adjusted_weight_long = base_weight_long * long_weight_factor;
    adjusted_weight_lat = base_weight_lat * lat_weight_factor;

    % 归一化权重，确保总和为1
    total_weight = adjusted_weight_long + adjusted_weight_lat;
    weight_long = single(adjusted_weight_long / total_weight);
    weight_lat = single(adjusted_weight_lat / total_weight);

    % 限制权重范围，相邻车道场景下横向权重应该占主导
    weight_long = max(min(weight_long, 0.5), 0.05);  % 纵向权重范围 [0.05, 0.5]
    weight_lat = single(1.0 - weight_long);          % 横向权重范围 [0.5, 0.95]

    fprintf('  [最终权重] 纵向: %.2f, 横向: %.2f\n', weight_long, weight_lat);
end