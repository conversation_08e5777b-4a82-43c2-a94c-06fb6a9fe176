% 测试动态权重调整功能
% 验证不同场景下权重的变化

clc; clear; close all;

fprintf('=== 动态权重调整测试 ===\n\n');

% 基础参数
obj_id = uint64(123);
clock = 0.0;

%% 测试场景1: 正常跟车场景
fprintf('场景1: 正常跟车\n');
fprintf('-------------------\n');
x_lead = 20; v_host = 15; yawrate = 0;
vx_lead = 15; vy_lead = 0; y_lead = 0;
threat1 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat1);

%% 测试场景2: 近距离场景
fprintf('场景2: 近距离跟车 (10m)\n');
fprintf('-------------------\n');
x_lead = 10; v_host = 15; yawrate = 0;
vx_lead = 12; vy_lead = 0; y_lead = 0;
threat2 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat2);

%% 测试场景3: 高速场景
fprintf('场景3: 高速行驶 (30m/s)\n');
fprintf('-------------------\n');
x_lead = 25; v_host = 30; yawrate = 0;
vx_lead = 28; vy_lead = 0; y_lead = 0;
threat3 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat3);

%% 测试场景4: 朝向自车道变道
fprintf('场景4: 右侧车辆朝自车道变道\n');
fprintf('-------------------\n');
x_lead = 15; v_host = 20; yawrate = 0;
vx_lead = 20; vy_lead = -0.8; y_lead = 2.5;  % 右侧车道，向左变道
threat4 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat4);

%% 测试场景5: 远离自车道变道
fprintf('场景5: 右侧车辆远离自车道变道\n');
fprintf('-------------------\n');
x_lead = 15; v_host = 20; yawrate = 0;
vx_lead = 20; vy_lead = 0.8; y_lead = 2.5;   % 右侧车道，向右变道
threat5 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat5);

%% 测试场景6: 激进相对速度
fprintf('场景6: 前车急刹车场景\n');
fprintf('-------------------\n');
x_lead = 18; v_host = 25; yawrate = 0;
vx_lead = 18; vy_lead = 0; y_lead = 0;       % 相对速度 = -7 m/s
threat6 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat6);

%% 测试场景7: 前车更快场景
fprintf('场景7: 前车明显更快\n');
fprintf('-------------------\n');
x_lead = 20; v_host = 15; yawrate = 0;
vx_lead = 22; vy_lead = 0; y_lead = 0;       % 相对速度 = +7 m/s
threat7 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat7);

%% 测试场景8: 横向距离很近
fprintf('场景8: 横向距离很近的邻车\n');
fprintf('-------------------\n');
x_lead = 15; v_host = 20; yawrate = 0;
vx_lead = 19; vy_lead = 0; y_lead = 1.8;     % 横向距离很近
threat8 = AdjObjThreatCal(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock);
fprintf('威胁值: %.3f\n\n', threat8);

%% 综合场景对比
fprintf('=== 场景威胁值对比 ===\n');
scenarios = {'正常跟车', '近距离', '高速', '朝向变道', '远离变道', '急刹车', '前车更快', '横向很近'};
threats = [threat1, threat2, threat3, threat4, threat5, threat6, threat7, threat8];

for i = 1:length(scenarios)
    fprintf('%s: %.3f\n', scenarios{i}, threats(i));
end

fprintf('\n=== 测试完成 ===\n');
