function AdjObjThreat = AdjObjThreatCal_improved(obj_id, x_lead, v_host, yawrate, vx_lead, vy_lead, y_lead, clock, varargin)
% 改进版邻车威胁评估函数
% 新增功能：
% 1. 修正TTC计算
% 2. 增加意图识别
% 3. 考虑加速度信息
% 4. 动态权重调整
% 5. 多时间尺度预测
% 6. 不确定性处理

    % 解析可选参数
    p = inputParser;
    addParameter(p, 'ax_lead', 0, @isnumeric);  % 前车纵向加速度
    addParameter(p, 'ay_lead', 0, @isnumeric);  % 前车横向加速度
    addParameter(p, 'lane_change_intent', 0, @isnumeric);  % 变道意图 [0,1]
    addParameter(p, 'road_curvature', 0, @isnumeric);  % 道路曲率
    addParameter(p, 'weather_factor', 1, @isnumeric);  % 天气因子 [0.5,1]
    parse(p, varargin{:});
    
    ax_lead = single(p.Results.ax_lead);
    ay_lead = single(p.Results.ay_lead);
    lane_change_intent = single(p.Results.lane_change_intent);
    road_curvature = single(p.Results.road_curvature);
    weather_factor = single(p.Results.weather_factor);
    
    current_t = single(clock);
    
    % 改进的边界条件检查
    max_detection_range = single(50);  % 增加检测范围
    max_lat_distance = single(5);      % 增加横向检测范围
    
    returnCond = (obj_id == uint64(0)) || (x_lead > max_detection_range) || ...
                 (abs(y_lead) > max_lat_distance) || (x_lead < 0.1);
    if returnCond
        AdjObjThreat = single(0);
        return;
    end

    %% 1. 改进的纵向威胁计算
    tau = single(2.5);  % 增加时间常数，更保守
    v_lead = max(0.1, vx_lead);
    x_lead = max(0.5, x_lead);
    min_rel_spd = single(-2);  % 更严格的相对速度阈值
    
    rel_spd = v_lead - v_host;
    
    % 修正TTC计算
    if rel_spd < min_rel_spd  % 前车明显更慢，有碰撞风险
        TTC = single(-x_lead / rel_spd);  % 修正：TTC = distance / |relative_speed|
        TTC = max(min(TTC, 20), 0.1);    % 限制TTC范围
        
        % 考虑加速度的影响
        if ax_lead ~= 0
            % 使用运动学方程计算更精确的TTC
            a_rel = ax_lead;  % 相对加速度（假设自车加速度为0）
            discriminant = rel_spd^2 + 2 * a_rel * x_lead;
            if discriminant >= 0 && a_rel ~= 0
                TTC_kinematic = (-rel_spd + sqrt(discriminant)) / a_rel;
                if TTC_kinematic > 0 && TTC_kinematic < TTC
                    TTC = TTC_kinematic;
                end
            end
        end
    else
        TTC = single(21);  % 无碰撞风险
    end
    
    % 纵向威胁计算
    if rel_spd > 5  % 前车明显更快，威胁很小
        long_threat = single(0);
    else
        long_threat = single(exp(-TTC / tau));
        % 考虑天气因素
        long_threat = long_threat * weather_factor;
        long_threat = max(min(long_threat, 1), 0);
    end

    %% 2. 改进的横向威胁计算
    % 多时间尺度预测
    t_pred_short = single(1.0);   % 短期预测
    t_pred_long = single(2.5);    % 长期预测
    w_lane = single(2.8);         % 使用你更新的车道宽度
    sigma_y = single(w_lane / 3);
    
    % 考虑道路曲率的影响
    vy_lead_correct = vy_lead - yawrate * x_lead - road_curvature * vx_lead * x_lead;
    
    % 短期预测位置
    y_pred_short = y_lead + t_pred_short * vy_lead_correct + 0.5 * ay_lead * t_pred_short^2;
    % 长期预测位置  
    y_pred_long = y_lead + t_pred_long * vy_lead_correct + 0.5 * ay_lead * t_pred_long^2;
    
    % 计算边到边距离（考虑车辆宽度）
    veh_half_width = single(1.0);  % 车辆半宽
    
    function dist = calc_side_to_side_dist(y_pos)
        if abs(y_pos) < veh_half_width * 2
            dist = single(0);  % 车辆重叠
        else
            dist = single(abs(y_pos) - veh_half_width * 2);
        end
    end
    
    dist_short = calc_side_to_side_dist(y_pred_short);
    dist_long = calc_side_to_side_dist(y_pred_long);
    
    % 短期和长期横向威胁
    lat_threat_short = single(exp(-0.5 * (dist_short / sigma_y)^2));
    lat_threat_long = single(exp(-0.5 * (dist_long / sigma_y)^2));
    
    % 综合横向威胁（短期权重更高）
    lat_threat = 0.7 * lat_threat_short + 0.3 * lat_threat_long;
    lat_threat = max(min(lat_threat, 1), 0);

    %% 3. 意图识别和动态权重调整
    % 基于横向速度和加速度判断变道意图
    intent_threshold = single(0.3);  % m/s
    if abs(vy_lead_correct) > intent_threshold || lane_change_intent > 0.5
        % 检测到变道意图，增加横向威胁权重
        weight_long = single(0.2);
        weight_lat = single(0.8);
        
        % 如果是朝向自车道变道，进一步增加威胁
        if (y_lead > 0 && vy_lead_correct < -intent_threshold) || ...
           (y_lead < 0 && vy_lead_correct > intent_threshold)
            lat_threat = lat_threat * 1.2;  % 威胁放大
        end
    else
        % 正常跟车状态
        weight_long = single(0.4);
        weight_lat = single(0.6);
    end

    %% 4. 最终威胁计算
    AdjObjThreat = single(weight_long * long_threat + weight_lat * lat_threat);
    
    % 应用全局调节因子
    if x_lead < 10  % 距离很近时增加威胁
        AdjObjThreat = AdjObjThreat * 1.1;
    end
    
    % 限制输出范围
    AdjObjThreat = max(min(AdjObjThreat, 1), 0);
    
    % 调试输出
    if nargout == 0 || any(strcmp('debug', varargin))
        fprintf('=== 威胁评估详情 ===\n');
        fprintf('TTC = %.2f s, 纵向威胁 = %.3f\n', TTC, long_threat);
        fprintf('短期横向距离 = %.2f m, 长期横向距离 = %.2f m\n', dist_short, dist_long);
        fprintf('横向威胁 = %.3f (短期: %.3f, 长期: %.3f)\n', lat_threat, lat_threat_short, lat_threat_long);
        fprintf('权重 - 纵向: %.1f, 横向: %.1f\n', weight_long, weight_lat);
        fprintf('最终威胁值 = %.3f, 时间 = %.2f\n', AdjObjThreat, current_t);
        fprintf('==================\n');
    end

end
