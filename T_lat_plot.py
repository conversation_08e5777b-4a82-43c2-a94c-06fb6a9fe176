import numpy as np
import matplotlib.pyplot as plt

# 参数设置
W_lane = 3.5  # Lane width (meters)
sigma_y = W_lane / 3  # Threat region width parameter

# 创建距离数组
d = np.linspace(-5, 5, 1000)  # Create 1000 points from -5m to 5m

# 计算威胁值
T_lat = np.exp(-0.5 * (d / sigma_y) ** 2)

# 创建图形
plt.figure(figsize=(10, 6))
plt.plot(d, T_lat, 'b-', linewidth=2)
plt.xlabel('Lateral Distance (m)')
plt.ylabel('Threat Level $T_{lat}$')
plt.title('Lateral Threat Function $T_{lat}(d) = exp(-0.5*(d/σ_y)^2)$')
plt.grid(True)

# 标记关键点
plt.plot([-sigma_y, sigma_y], [np.exp(-0.5), np.exp(-0.5)], 'ro', markersize=8)
plt.text(-sigma_y-0.3, np.exp(-0.5), '$σ_y$', fontsize=12)
plt.text(sigma_y+0.1, np.exp(-0.5), '$σ_y$', fontsize=12)

# 添加车道线标记
plt.axvline(x=-W_lane/2, color='r', linestyle='--', linewidth=1.5)
plt.axvline(x=W_lane/2, color='r', linestyle='--', linewidth=1.5)
plt.text(-W_lane/2-0.5, 0.1, 'Left Lane', fontsize=10)
plt.text(W_lane/2+0.1, 0.1, 'Right Lane', fontsize=10)

# 设置坐标轴范围
plt.xlim(-5, 5)
plt.ylim(0, 1.1)

# 添加网格
plt.grid(True, linestyle='--', alpha=0.7)

# 显示图形
plt.tight_layout()
plt.show()

# 打印一些关键点的值
print("Lateral Threat Function Key Values:")
print(f"d = 0.0m: T_lat = {np.exp(-0.5 * (0 / sigma_y)**2):.3f}")
print(f"d = ±{sigma_y:.2f}m: T_lat = {np.exp(-0.5):.3f}")
print(f"d = ±{W_lane/2:.2f}m (Lane boundary): T_lat = {np.exp(-0.5 * (W_lane/2 / sigma_y)**2):.3f}")
print(f"d = ±3.0m: T_lat = {np.exp(-0.5 * (3.0 / sigma_y)**2):.3f}")
print(f"d = ±5.0m: T_lat = {np.exp(-0.5 * (5.0 / sigma_y)**2):.6f}")